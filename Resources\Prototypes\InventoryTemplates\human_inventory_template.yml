﻿- type: inventoryTemplate
  id: human
  slots:
    - name: shoes
      slotTexture: shoes
      slotFlags: FEET
      stripTime: 3
      uiWindowPos: 1,0
      strippingWindowPos: 1,3
      displayName: Shoes
    - name: jumpsuit
      slotTexture: uniform
      slotFlags: INNERCLOTHING
      stripTime: 6
      uiWindowPos: 0,1
      strippingWindowPos: 0,2
      displayName: Jumpsuit
    - name: outerClothing
      slotTexture: suit
      slotFlags: OUTERCLOTHING
      stripTime: 6
      uiWindowPos: 1,1
      strippingWindowPos: 1,2
      displayName: Suit
    - name: gloves
      slotTexture: gloves
      slotFlags: GLOVES
      uiWindowPos: 2,1
      strippingWindowPos: 2,2
      displayName: Gloves
    - name: neck
      slotTexture: neck
      slotFlags: NECK
      uiWindowPos: 0,2
      strippingWindowPos: 0,1
      displayName: Neck
    - name: mask
      slotTexture: mask
      slotFlags: MASK
      uiWindowPos: 1,2
      strippingWindowPos: 1,1
      displayName: Mask
    - name: eyes
      slotTexture: glasses
      slotFlags: EYES
      stripTime: 3
      uiWindowPos: 0,3
      strippingWindowPos: 0,0
      displayName: Eyes
    - name: ears
      slotTexture: ears
      slotFlags: EARS
      stripTime: 3
      uiWindowPos: 2,2
      strippingWindowPos: 2,0
      displayName: Ears
    - name: head
      slotTexture: head
      slotFlags: HEAD
      uiWindowPos: 1,3
      strippingWindowPos: 1,0
      displayName: Head
#PIRATE START
    - name: head1
      slotTexture: head
      slotFlags: HEAD1
      uiWindowPos: 2,0
      strippingWindowPos: 2,1
      displayName: Head 1
    - name: head2
      slotTexture: head
      slotFlags: HEAD2
      uiWindowPos: 2,3
      strippingWindowPos: 2,6
      displayName: Head 2
    - name: neck1
      slotTexture: neck
      slotFlags: NECK1
      uiWindowPos: 3,1
      strippingWindowPos: 0,6
      displayName: Hat 1
    - name: neck2
      slotTexture: neck
      slotFlags: NECK2
      uiWindowPos: 3,0
      strippingWindowPos: 1,6
      displayName: Hat 2
#PIRATE END
    - name: pocket1
      slotTexture: pocket
      fullTextureName: template_small
      slotFlags: POCKET
      slotGroup: MainHotbar
      stripTime: 3
      uiWindowPos: 0,3
      strippingWindowPos: 0,4
      dependsOn: jumpsuit
      displayName: Pocket 1
      stripHidden: true
    - name: pocket2
      slotTexture: pocket
      fullTextureName: template_small
      slotFlags: POCKET
      slotGroup: MainHotbar
      stripTime: 3
      uiWindowPos: 2,3
      strippingWindowPos: 1,4
      dependsOn: jumpsuit
      displayName: Pocket 2
      stripHidden: true
    - name: suitstorage
      slotTexture: suit_storage
      slotFlags:   SUITSTORAGE
      slotGroup: MainHotbar
      stripTime: 3
      uiWindowPos: 2,0
      strippingWindowPos: 2,5
      dependsOn: outerClothing
      dependsOnComponents:
      - type: AllowSuitStorage
      displayName: Suit Storage
    - name: id
      slotTexture: id
      fullTextureName: template_small
      slotFlags: IDCARD
      slotGroup: SecondHotbar
      stripTime: 6
      uiWindowPos: 2,1
      strippingWindowPos: 2,4
      dependsOn: jumpsuit
      displayName: ID
    - name: belt
      slotTexture: belt
      fullTextureName: template_small
      slotFlags: BELT
      slotGroup: SecondHotbar
      stripTime: 6
      uiWindowPos: 3,1
      strippingWindowPos: 1,5
      displayName: Belt
    - name: back
      slotTexture: back
      fullTextureName: template_small
      slotFlags: BACK
      slotGroup: SecondHotbar
      stripTime: 6
      uiWindowPos: 3,0
      strippingWindowPos: 0,5
      displayName: Back
